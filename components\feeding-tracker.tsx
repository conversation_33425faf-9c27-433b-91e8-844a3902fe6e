'use client';

import { useState, useEffect } from 'react';
import { petStorage } from '@/lib/storage';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Utensils, Plus, Calendar, Clock, Trash2, Zap } from 'lucide-react';

interface FeedingLog {
  id: string;
  timestamp: string;
  foodType: string;
  amount: string;
  notes: string;
}

export function FeedingTracker() {
  const { toast } = useToast();
  const [logs, setLogs] = useState<FeedingLog[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newLog, setNewLog] = useState({
    foodType: '',
    amount: '',
    notes: ''
  });

  useEffect(() => {
    const savedLogs = petStorage.getFeedingLogs();
    setLogs(savedLogs);
  }, []);

  const handleAddLog = () => {
    if (!newLog.foodType) return;

    const log: FeedingLog = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      foodType: newLog.foodType,
      amount: newLog.amount,
      notes: newLog.notes
    };

    petStorage.addFeedingLog(log);
    setLogs(petStorage.getFeedingLogs());

    setNewLog({ foodType: '', amount: '', notes: '' });
    setIsDialogOpen(false);
  };

  const handleDeleteLog = (id: string) => {
    petStorage.removeFeedingLog(id);
    setLogs(petStorage.getFeedingLogs());
  };

  const handleQuickAdd = () => {
    if (logs.length === 0) return;

    // Get the most recent feeding log (first in array since they're sorted by newest first)
    const lastLog = logs[0];

    const quickLog: FeedingLog = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      foodType: lastLog.foodType,
      amount: lastLog.amount,
      notes: lastLog.notes
    };

    petStorage.addFeedingLog(quickLog);
    setLogs(petStorage.getFeedingLogs());

    // Show success toast
    toast({
      title: "Feeding Added!",
      description: `Quick added: ${lastLog.foodType}${lastLog.amount ? ` (${lastLog.amount})` : ''}`,
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getTodayLogs = () => {
    const today = new Date().toDateString();
    return logs.filter(log => new Date(log.timestamp).toDateString() === today);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Feeding Tracker</h2>
          <p className="text-gray-600">Monitor your pet's meals and feeding schedule</p>
        </div>
        <div className="flex space-x-2">
          {logs.length > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    onClick={handleQuickAdd}
                    className="border-orange-200 text-orange-700 hover:bg-orange-50"
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Add
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add a copy of your last feeding with current time</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-orange-600 hover:bg-orange-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Feeding
              </Button>
            </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Record New Feeding</DialogTitle>
              <DialogDescription>
                Add details about your pet's meal
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="foodType">Food Type *</Label>
                <Select value={newLog.foodType} onValueChange={(value) => setNewLog({ ...newLog, foodType: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select food type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dry-kibble">Dry Kibble</SelectItem>
                    <SelectItem value="wet-food">Wet Food</SelectItem>
                    <SelectItem value="treats">Treats</SelectItem>
                    <SelectItem value="raw-food">Raw Food</SelectItem>
                    <SelectItem value="prescription">Prescription Diet</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="amount">Amount</Label>
                <Input
                  id="amount"
                  value={newLog.amount}
                  onChange={(e) => setNewLog({ ...newLog, amount: e.target.value })}
                  placeholder="e.g., 1/2 cup, 1 can, 50g"
                />
              </div>
              <div>
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea
                  id="notes"
                  value={newLog.notes}
                  onChange={(e) => setNewLog({ ...newLog, notes: e.target.value })}
                  placeholder="Any additional notes about this feeding..."
                  className="min-h-[80px]"
                />
              </div>
              <div className="flex space-x-2 pt-4">
                <Button onClick={handleAddLog} className="flex-1" disabled={!newLog.foodType}>
                  Add Feeding Log
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Today's Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Today's Feedings</span>
          </CardTitle>
          <CardDescription>
            {getTodayLogs().length} meals recorded today
          </CardDescription>
        </CardHeader>
        <CardContent>
          {getTodayLogs().length === 0 ? (
            <p className="text-gray-500 text-center py-4">No feedings recorded today</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {getTodayLogs().map((log) => {
                const { time } = formatTimestamp(log.timestamp);
                return (
                  <div key={log.id} className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        {log.foodType.replace('-', ' ')}
                      </Badge>
                      <span className="text-sm text-gray-600">{time}</span>
                    </div>
                    {log.amount && (
                      <p className="text-sm font-medium text-gray-900">{log.amount}</p>
                    )}
                    {log.notes && (
                      <p className="text-sm text-gray-600 mt-1">{log.notes}</p>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* All Feeding Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Utensils className="h-5 w-5" />
            <span>Feeding History</span>
          </CardTitle>
          <CardDescription>
            Complete record of all feedings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {logs.length === 0 ? (
            <div className="text-center py-8">
              <Utensils className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500">No feeding logs yet</p>
              <p className="text-sm text-gray-400">Add your first feeding record to get started</p>
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((log) => {
                const { date, time } = formatTimestamp(log.timestamp);
                return (
                  <div key={log.id} className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
                          {log.foodType.replace('-', ' ')}
                        </Badge>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{date}</span>
                          <Clock className="h-4 w-4" />
                          <span>{time}</span>
                        </div>
                      </div>
                      {log.amount && (
                        <p className="text-sm font-medium text-gray-900 mb-1">Amount: {log.amount}</p>
                      )}
                      {log.notes && (
                        <p className="text-sm text-gray-600">{log.notes}</p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteLog(log.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}